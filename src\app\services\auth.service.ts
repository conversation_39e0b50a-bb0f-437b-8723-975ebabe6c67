import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { BehaviorSubject, Observable, throwError, of, EMPTY } from 'rxjs';
import { map, catchError, tap, distinctUntilChanged } from 'rxjs/operators';
import { BrowserStorageService } from './browser-storage.service';

// Define interfaces locally since we removed the User model
interface User {
  id: number;
  firstname: string;
  lastname: string;
  email: string;
  phone?: string;
  avatar?: string;
  name?: string;
  fullName?: string;
  created_at?: string;
  updated_at?: string;
  is_active?: boolean;
  is_admin?: boolean;
}

export interface AuthResponse {
  user?: User;
  token?: string;
  url?: string;
  data?: any; // Response data that may contain user information or other data
}

export interface ProfileUpdateData {
  firstname: string;
  lastname: string;
  email: string;
  phone?: string;
}

export interface RegisterData {
  firstname: string;
  lastname: string;
  email: string;
  password: string;
  password_confirmation: string;
}

export interface ResetPasswordData {
  email: string;
  password: string;
  password_confirmation: string;
  token: string;
}



// Storage keys as constants
const STORAGE_KEYS = {
  USER: 'user',
  TOKEN: 'token',
  REFRESH_TOKEN: 'refresh_token'
} as const;

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly currentUserSubject = new BehaviorSubject<User | null>(null);
  private readonly authTokenSubject = new BehaviorSubject<string | null>(null);
  private readonly authStateSubject = new BehaviorSubject<boolean>(false);
  private refreshTokenTimeout?: any;

  readonly currentUser$ = this.currentUserSubject.asObservable().pipe(distinctUntilChanged());
  readonly authToken$ = this.authTokenSubject.asObservable().pipe(distinctUntilChanged());
  readonly authState$ = this.authStateSubject.asObservable().pipe(distinctUntilChanged());
  readonly isAuthenticated$ = this.authStateSubject.asObservable().pipe(distinctUntilChanged());

  // Configure API URL based on environment
  private readonly apiUrl!: string; // Using definite assignment assertion

  // Initialize API URL in constructor to use isPlatformBrowser

  // Path for avatar uploads
  private readonly avatarPath = 'uploads/avatars';

  constructor(
    private readonly http: HttpClient,
    @Inject(PLATFORM_ID) private platformId: Object,
    private storage: BrowserStorageService
  ) {
    // Initialize API URL based on platform
    this.apiUrl = isPlatformBrowser(this.platformId)
      ? (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
        ? 'http://localhost:8000/api'
        : 'https://api.yourproductionurl.com/api')
      : 'http://localhost:8000/api'; // Default to development URL during SSR

    this.initializeFromStorage();
    this.startRefreshTokenTimer();
    console.log('Auth service initialized with API URL:', this.apiUrl);
    console.log('Running in browser:', isPlatformBrowser(this.platformId));
  }

  // Add getter for apiUrl
  getApiUrl(): string {
    return this.apiUrl;
  }

  // Add getter for avatar URL with improved error handling
  getAvatarUrl(filename: string): string {
    if (!filename || filename.trim() === '') {
      console.log('No filename provided, returning default avatar');
      return 'assets/images/default-avatar.png';
    }

    // Clean the filename
    const cleanFilename = filename.trim();

    // If it's already a full URL (Google, Facebook, etc.)
    if (cleanFilename.startsWith('http://') || cleanFilename.startsWith('https://')) {
      console.log('Using external URL for avatar:', cleanFilename);
      return cleanFilename;
    }

    // If it's a relative path, construct the full URL
    try {
      const baseUrl = this.apiUrl.replace('/api', '');
      const avatarUrl = `${baseUrl}/${this.avatarPath}/${cleanFilename}`;
      console.log('Constructed avatar URL:', avatarUrl);
      return avatarUrl;
    } catch (error) {
      console.error('Error constructing avatar URL:', error);
      return 'assets/images/default-avatar.png';
    }
  }

  private initializeFromStorage(): void {
    // Skip if not in browser environment
    if (!isPlatformBrowser(this.platformId)) {
      console.log('Not in browser environment, skipping storage initialization');
      return;
    }

    try {
      const [storedUser, storedToken] = this.getStoredCredentials();
      console.log('Initializing from storage - User:', storedUser ? 'found' : 'not found', 'Token:', storedToken ? 'found' : 'not found');

      if (storedToken) {
        // Validate token format before using it
        if (typeof storedToken === 'string' && storedToken.split('.').length === 3) {
          console.log('Valid token format found in storage');
          this.authTokenSubject.next(storedToken);
          this.authStateSubject.next(true);

          // Process user data if available
          if (storedUser) {
            console.log('User data found in storage, normalizing');
            // Ensure we normalize the user data to preserve is_admin property
            const normalizedUser = this.normalizeUserData(storedUser);
            console.log('Normalized user from storage:', normalizedUser);
            console.log('Is admin:', normalizedUser.is_admin);
            this.currentUserSubject.next(normalizedUser);
          } else {
            console.log('No user data found in storage, will try to fetch from API');
            // Try to fetch user data from API
            this.fetchCurrentUser().subscribe({
              next: (user) => {
                if (user) {
                  console.log('Successfully fetched user data from API during initialization');
                } else {
                  console.warn('Failed to fetch user data from API during initialization');
                }
              },
              error: (error) => {
                console.error('Error fetching user data from API during initialization:', error);
              }
            });
          }
        } else {
          console.warn('Invalid token format found in storage');
          // Don't clear storage yet, just don't initialize with invalid token
        }
      } else {
        console.log('No token found in storage, user is not logged in');
      }
    } catch (error) {
      console.error('Error initializing from storage:', error);
      // Don't clear storage on initialization error, just log it
    }
  }

  private getStoredCredentials(): [User | null, string | null] {
    try {
      const userStr = this.storage.getItem(STORAGE_KEYS.USER);
      const token = this.storage.getItem(STORAGE_KEYS.TOKEN);

      console.log('Reading credentials from storage');
      console.log('User string found:', !!userStr);
      console.log('Token found:', !!token);

      let parsedUser = null;
      if (userStr) {
        try {
          parsedUser = JSON.parse(userStr);
          console.log('Successfully parsed user data from storage');
          // Log the is_admin property specifically
          console.log('User is_admin property:', parsedUser.is_admin);
        } catch (parseError) {
          console.error('Error parsing user JSON from storage:', parseError);
        }
      }

      return [parsedUser, token];
    } catch (error) {
      console.error('Error reading from storage:', error);
      return [null, null];
    }
  }

  private clearStorage(): void {
    try {
      // Clear auth state first to update UI immediately
      this.currentUserSubject.next(null);
      this.authTokenSubject.next(null);
      this.authStateSubject.next(false);

      // Then clear storage
      this.storage.removeItem(STORAGE_KEYS.USER);
      this.storage.removeItem(STORAGE_KEYS.TOKEN);

      console.log('Auth state cleared, authState is now:', this.authStateSubject.value);
    } catch (error) {
      console.error('Error clearing storage:', error);
    }
  }

  normalizeUserData(userData: any): User {
    console.log('Normalizing raw user data:', userData);

    // Check if userData is null or undefined
    if (!userData) {
      console.warn('userData is null or undefined');
      return {
        id: 1,
        firstname: '',
        lastname: '',
        email: '',
        avatar: 'assets/images/default-avatar.png',
        name: 'User',
        fullName: 'User'
      };
    }

    // Unwrap nested data structures
    let userInfo = userData;
    if (userData.data) {
      console.log('Found data wrapper, unwrapping');
      userInfo = typeof userData.data === 'object' ? userData.data : userData;
    }
    if (userInfo.user) {
      console.log('Found user wrapper, unwrapping');
      userInfo = typeof userInfo.user === 'object' ? userInfo.user : userInfo;
    }

    // Log the structure we're working with
    console.log('Processing user info structure:', userInfo);
    console.log('Available fields:', Object.keys(userInfo));

    // Extract name components
    let extractedFirstName = '';
    let extractedLastName = '';

    // Try to get name components from various possible fields
    if (userInfo.firstname || userInfo.first_name || userInfo.firstName) {
      extractedFirstName = userInfo.firstname || userInfo.first_name || userInfo.firstName;
    } else if (userInfo.name && typeof userInfo.name === 'string') {
      const nameParts = userInfo.name.split(' ');
      extractedFirstName = nameParts[0] || '';
      extractedLastName = nameParts.slice(1).join(' ') || '';
    }

    if (userInfo.lastname || userInfo.last_name || userInfo.lastName) {
      extractedLastName = userInfo.lastname || userInfo.last_name || userInfo.lastName;
    }

    // Handle avatar URL with proper fallback chain
    let avatarUrl = '';
    if (userInfo.avatar_url && typeof userInfo.avatar_url === 'string') {
      avatarUrl = userInfo.avatar_url;
    } else if (userInfo.avatar && typeof userInfo.avatar === 'string') {
      // If it's already a full URL, use it directly
      if (userInfo.avatar.startsWith('http')) {
        avatarUrl = userInfo.avatar;
      } else {
        // Otherwise, construct the URL using our helper method
        avatarUrl = this.getAvatarUrl(userInfo.avatar);
      }
    } else {
      avatarUrl = 'assets/images/default-avatar.png';
    }

    console.log('Processed avatar URL:', avatarUrl);

    // Normalize the user object
    const normalizedUser: User = {
      id: userInfo.id || userInfo.user_id || 1,
      firstname: extractedFirstName,
      lastname: extractedLastName,
      email: userInfo.email || '',
      phone: userInfo.phone || '',
      avatar: avatarUrl,
      created_at: userInfo.created_at,
      updated_at: userInfo.updated_at,
      is_active: userInfo.is_active !== undefined ? userInfo.is_active : true,
      is_admin: userInfo.is_admin !== undefined ? userInfo.is_admin : false,
      name: '',
      fullName: ''
    };



    // Add name and fullName for compatibility
    normalizedUser.name = `${normalizedUser.firstname} ${normalizedUser.lastname}`.trim() || 'User';
    normalizedUser.fullName = normalizedUser.name;

    console.log('Normalized user data:', normalizedUser);
    return normalizedUser;
  }

  private updateStorageAndState(response: AuthResponse): void {
    if (response.token) {
      // Validate token format before using it
      if (typeof response.token === 'string') {
        console.log('Valid token format, updating storage and state');

        // Update memory state
        this.authTokenSubject.next(response.token);
        this.authStateSubject.next(true);

        // Update storage
        this.storage.setItem(STORAGE_KEYS.TOKEN, response.token);
      } else {
        console.warn('Invalid token format received, not updating token');
      }
    }

    if (response.user) {
      const normalizedUser = this.normalizeUserData(response.user);



      // Update memory state
      this.currentUserSubject.next(normalizedUser);

      // Update storage
      this.storage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));

      console.log('Updated user in storage:', normalizedUser);
    }
  }

  private getAuthHeaders(): HttpHeaders {
    const token = this.authTokenSubject.value;
    let headers = new HttpHeaders()
      .set('Accept', 'application/json')
      .set('Content-Type', 'application/json');

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  isLoggedIn(): boolean {
    console.log('Checking if user is logged in...');

    // First check if we have a token in memory
    const memoryToken = this.authTokenSubject.value;
    if (memoryToken) {
      console.log('Token found in memory, user is logged in');
      return true;
    }

    // If not, check storage
    try {
      const token = this.storage.getItem(STORAGE_KEYS.TOKEN);
      console.log('Token in storage:', token ? 'exists' : 'not found');

      if (token) {
        // If token exists in storage but not in memory, update memory
        console.log('Token found in storage, updating memory');
        this.authTokenSubject.next(token);
        this.authStateSubject.next(true);

        // Also check for user data
        const userStr = this.storage.getItem(STORAGE_KEYS.USER);
        if (userStr) {
          try {
            const userData = JSON.parse(userStr);
            // Make sure to normalize the user data to preserve is_admin property
            const normalizedUser = this.normalizeUserData(userData);
            this.currentUserSubject.next(normalizedUser);
            console.log('User data loaded and normalized from storage');
            console.log('Is admin:', normalizedUser.is_admin);
          } catch (e) {
            console.error('Error parsing user data from storage:', e);
          }
        }

        return true;
      }
    } catch (error) {
      console.error('Error checking storage for token:', error);
    }

    console.log('No token found, user is not logged in');
    return false;
  }

  // Alias for isLoggedIn for better semantics
  isAuthenticated(): boolean {
    return this.isLoggedIn();
  }

  getAuthToken(): string | null {
    // First check if we have a token in memory
    if (this.authTokenSubject.value) {
      return this.authTokenSubject.value;
    }

    // If not, check storage
    try {
      const token = this.storage.getItem(STORAGE_KEYS.TOKEN);
      if (token) {
        // If token exists in storage but not in memory, update memory
        this.authTokenSubject.next(token);
        this.authStateSubject.next(true);

        // Also check for user data to ensure admin status is loaded
        const userStr = this.storage.getItem(STORAGE_KEYS.USER);
        if (userStr) {
          try {
            const userData = JSON.parse(userStr);
            // Make sure to normalize the user data to preserve is_admin property
            const normalizedUser = this.normalizeUserData(userData);
            this.currentUserSubject.next(normalizedUser);
            console.log('User data loaded and normalized from storage in getAuthToken');
            console.log('Is admin:', normalizedUser.is_admin);
          } catch (e) {
            console.error('Error parsing user data from storage in getAuthToken:', e);
          }
        }

        return token;
      }
    } catch (error) {
      console.error('Error checking storage for token:', error);
    }

    return null;
  }

  getCurrentUser(): Observable<any> {
    const token = this.authTokenSubject.value;

    if (!token) {
      console.log('No token available, returning null user');
      return of(null);
    }

    // First try to get from storage
    const storedUser = this.refreshUserFromStorage();
    if (storedUser) {
      console.log('Using stored user data:', storedUser);
      this.currentUserSubject.next(storedUser);

      // Return the stored user immediately while we fetch the latest data
      const storedUserObservable = of(storedUser);

      // Fetch the latest user data from the API in the background
      this.fetchCurrentUser().subscribe({
        next: (user) => {
          if (user) {
            console.log('Updated user data from API');
          }
        },
        error: (error) => {
          console.error('Error updating user data:', error);
        }
      });

      return storedUserObservable;
    }

    // If no stored user, fetch from API
    return this.fetchCurrentUser();
  }

  /**
   * Get the current user value synchronously (not as Observable)
   * @returns User object or null
   */
  getCurrentUserValue(): User | null {
    // First check if we have user data in memory
    const currentUser = this.currentUserSubject.value;
    if (currentUser) {
      return currentUser;
    }

    // If not in memory, try to get from storage
    const storedUser = this.refreshUserFromStorage();
    if (storedUser) {
      this.currentUserSubject.next(storedUser);
      return storedUser;
    }

    return null;
  }

  fetchCurrentUser(): Observable<any> {
    console.log('Fetching user profile from API...');
    return this.http.get<any>(
      `${this.apiUrl}/profile`,
      {
        headers: this.getAuthHeaders(),
        withCredentials: true
      }
    ).pipe(
      map(response => {
        console.log('Get current user response (raw):', response);

        // Check if the response is empty or null
        if (!response) {
          console.warn('Empty response from API');
          return null;
        }

        // Log the structure of the response to help debug
        console.log('Response type:', typeof response);
        console.log('Response keys:', Object.keys(response));

        // Enhanced response handling with more detailed logging
        let userData = null;

        // Check for common response structures
        if (response.user) {
          console.log('Found user property in response');
          userData = response.user;
        } else if (response.data) {
          console.log('Found data property in response');

          // Check if data is the user object or contains a user object
          if (typeof response.data === 'object') {
            if (response.data.user) {
              console.log('Found user property in data');
              userData = response.data.user;
            } else if (response.data.firstname || response.data.email) {
              console.log('Data appears to be the user object itself');
              userData = response.data;
            }
          } else {
            console.warn('Data property is not an object:', response.data);
          }
        } else if (response.firstname || response.email) {
          // The response itself might be the user object
          console.log('Response appears to be the user object directly');
          userData = response;
        }

        console.log('Extracted user data from response:', userData);

        // Check if we have any user data at all
        if (!userData) {
          console.warn('No user data found in response');
          return null;
        }

        const normalizedUser = this.normalizeUserData(userData);
        console.log('Normalized user data:', normalizedUser);

        // Update memory state
        this.currentUserSubject.next(normalizedUser);

        // Update storage
        this.storage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));

        return normalizedUser;
      }),
      catchError(error => {
        console.error('Error fetching user profile:', error);
        return of(null);
      })
    );
  }

  private refreshUserFromStorage(): User | null {
    try {
      const userStr = this.storage.getItem(STORAGE_KEYS.USER);
      if (userStr) {
        const user = JSON.parse(userStr);
        return this.normalizeUserData(user);
      }
    } catch (error) {
      console.error('Error refreshing user from storage:', error);
    }
    return null;
  }

  login(email: string, password: string): Observable<AuthResponse> {
    console.log('Login attempt with email:', email);

    // Clear any existing auth data to prevent conflicts
    this.clearStorage();

    return this.http.post<AuthResponse>(
      `${this.apiUrl}/login`,
      { email, password },
      {
        headers: new HttpHeaders()
          .set('Accept', 'application/json')
          .set('Content-Type', 'application/json'),
        withCredentials: true
      }
    ).pipe(
      tap(response => {
        console.log('Login response:', response);
        console.log('Response token:', response.token);
        console.log('Response user:', response.user);

        if (response && response.token && response.user) {
          // Update storage and state
          this.updateStorageAndState(response);

          // Start refresh token timer
          this.startRefreshTokenTimer();

          // Dispatch auth state changed event
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('auth-state-changed', {
              detail: { isLoggedIn: true }
            }));
          }
        } else {
          console.error('Invalid login response structure:', response);
        }
      }),
      catchError(this.handleError)
    );
  }

  register(userData: RegisterData): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.apiUrl}/register`, userData)
      .pipe(
        tap(response => this.updateStorageAndState(response)),
        catchError(this.handleError)
      );
  }

  logout(): void {
    this.stopRefreshTokenTimer();
    this.clearStorage();

    const token = this.getAuthToken();
    if (token) {
      this.http.post(`${this.apiUrl}/logout`, {}, {
        headers: this.getAuthHeaders()
      }).pipe(
        catchError(error => {
          console.error('Logout error:', error);
          return EMPTY;
        })
      ).subscribe();
    }
  }

  private handleError = (error: HttpErrorResponse): Observable<never> => {
    let errorMessage = 'An unknown error occurred';

    if (error.error instanceof ErrorEvent) {
      errorMessage = `Client error: ${error.error.message}`;
    } else {
      // Log the full error object for debugging
      console.log('Full error object:', error);

      // Check for detailed error information from our Laravel backend
      if (error.error && typeof error.error === 'object') {
        console.log('Error details from server:', error.error);

        if (error.error.error_details) {
          console.log('Detailed error:', error.error.error_details);
          console.log('Error file:', error.error.error_file);
          console.log('Error line:', error.error.error_line);

          // Include detailed error in the message for development
          errorMessage = `Server error: ${error.error.error_details} at ${error.error.error_file}:${error.error.error_line}`;
        } else if (error.error.message) {
          errorMessage = `Server error: ${error.error.message}`;
        } else {
          errorMessage = `Server error: ${error.status} - ${error.statusText}`;
        }
      } else {
        errorMessage = `Server error: ${error.status} - ${error.statusText}`;
      }

      if (error.status === 401) {
        // Check if this is a password validation error
        if (error.url?.includes('/user/password') && error.error?.message?.includes('Current password is incorrect')) {
          // Don't log out for incorrect current password
          errorMessage = 'Current password is incorrect';
        } else {
          // For other 401 errors, log the user out
          console.log('Unauthorized access, clearing storage and logging out');
          this.clearStorage();
          errorMessage = 'Unauthorized: Please log in again';
        }
      } else if (error.status === 403) {
        errorMessage = 'Forbidden: You don\'t have permission to perform this action';
      } else if (error.status === 404) {
        errorMessage = 'Resource not found';
      } else if (error.status === 422) {
        errorMessage = 'Validation error: Please check your input';
      } else if (error.status === 500) {
        // Keep the detailed error message for 500 errors
        if (!errorMessage.includes('Server error:')) {
          errorMessage = 'Server error: Please try again later';
        }
      }
    }

    console.error('Auth service error:', errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  // Update avatar method with improved error handling and response processing
  updateAvatar(formData: FormData): Observable<AuthResponse> {
    if (!this.isLoggedIn()) {
      return throwError(() => new Error('Not authenticated'));
    }

    console.log('Starting avatar upload in service');

    // Don't set Content-Type for FormData, browser will set it with boundary
    const headers = new HttpHeaders()
      .set('Accept', 'application/json')
      .set('Authorization', `Bearer ${this.getAuthToken()}`);

    // Log the FormData contents for debugging
    console.log('FormData contents:');
    formData.forEach((value, key) => {
      console.log(key, value);
    });

    return this.http.post<AuthResponse>(
      `${this.apiUrl}/upload-profile-image`,
      formData,
      {
        headers,
        withCredentials: true
      }
    ).pipe(
      map(response => {
        console.log('Avatar upload response (raw):', response);

        if (!response) {
          throw new Error('Empty response from server');
        }

        // Handle different response formats
        let userData = null;

        if (response.user) {
          userData = response.user;
        } else if (response.data) {
          userData = response.data.user || response.data;
        }

        if (!userData) {
          throw new Error('No user data in response');
        }

        const normalizedUser = this.normalizeUserData(userData);
        this.storage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));
        this.currentUserSubject.next(normalizedUser);

        return response;
      }),
      catchError(error => {
        console.error('Avatar upload error:', error);
        if (error instanceof HttpErrorResponse) {
          if (error.status === 413) {
            return throwError(() => new Error('File is too large. Maximum size is 2MB.'));
          } else if (error.status === 415) {
            return throwError(() => new Error('Invalid file type. Please upload an image file.'));
          }
        }
        return this.handleError(error);
      })
    );
  }

  // Update profile method
  updateProfile(data: ProfileUpdateData): Observable<any> {
    if (!this.isLoggedIn()) {
      return throwError(() => new Error('Not authenticated'));
    }

    console.log('Auth service sending update data:', data);
    console.log('Phone data received:', data.phone);

    // Ensure the data follows the backend's expected format
    const updateData = {
      firstname: data.firstname,
      lastname: data.lastname,
      email: data.email,
      phone: data.phone || ''
    };

    console.log('Auth service formatted update data:', updateData);
    console.log('Phone being sent to backend:', updateData.phone);

    return this.http.post<AuthResponse>(
      `${this.apiUrl}/update-settings`,
      updateData,
      {
        headers: this.getAuthHeaders(),
        withCredentials: true
      }
    ).pipe(
      map(response => {
        console.log('Profile update response (raw):', response);
        console.log('Response type:', typeof response);
        console.log('Response keys:', Object.keys(response));

        // Handle different response formats
        let userData = null;

        if (response.user) {
          console.log('Found user property in response');
          userData = response.user;
        } else if (response.data) {
          console.log('Found data property in response');
          userData = response.data;

          // Check if data contains a user object
          if (userData.user) {
            console.log('Found user property in data');
            userData = userData.user;
          }
        } else {
          // If no user or data property, try to use the response itself
          userData = response;
        }

        console.log('Extracted user data from response:', userData);

        if (userData) {
          const normalizedUser = this.normalizeUserData(userData);
          console.log('Normalized user data after profile update:', normalizedUser);
          this.storage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));
          this.currentUserSubject.next(normalizedUser);
        }

        return { user: userData, ...response };
      }),
      catchError(error => {
        console.error('Profile update error:', error);
        return this.handleError(error);
      })
    );
  }

  // This method has been replaced by the improved version below

  // Method to set the current user directly (used by auth callback)
  setCurrentUser(user: any): void {
    console.log('Setting current user:', user);
    if (!user) {
      console.warn('Attempted to set null/undefined user');
      return;
    }

    const normalizedUser = this.normalizeUserData(user);
    this.storage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));
    this.currentUserSubject.next(normalizedUser);
    console.log('User set:', normalizedUser);
  }

  // Method to set the token directly
  setToken(token: string): void {
    console.log('Setting token manually, length:', token ? token.length : 0);
    if (!token) {
      console.warn('Attempted to set null/undefined token');
      return;
    }

    // Validate token format - more lenient validation for Google tokens
    if (typeof token !== 'string') {
      console.error('Invalid token type, expected string but got:', typeof token);
      return;
    }

    // Log token format for debugging
    const tokenParts = token.split('.');
    console.log('Token parts count:', tokenParts.length);

    // Store token in storage
    this.storage.setItem(STORAGE_KEYS.TOKEN, token);
    console.log('Token stored in storage');

    // Update auth state
    this.authTokenSubject.next(token);
    this.authStateSubject.next(true);
    console.log('Token set, auth state updated to:', this.authStateSubject.value);
  }

  // Method to manually set user data from storage
  loadUserFromLocalStorage(): User | null {
    try {
      console.log('Attempting to load user from storage');
      const userStr = this.storage.getItem(STORAGE_KEYS.USER);
      if (!userStr) {
        console.warn('No user data found in storage');
        return null;
      }

      const userData = JSON.parse(userStr);
      console.log('Found user data in storage:', userData);

      const normalizedUser = this.normalizeUserData(userData);
      this.currentUserSubject.next(normalizedUser);
      console.log('Loaded user from storage:', normalizedUser);

      return normalizedUser;
    } catch (error) {
      console.error('Error loading user from storage:', error);
      return null;
    }
  }

  // Manual login method (used by auth callback and for manual login)
  manualLogin(user: any, token: string): void {
    console.log('Manual login called with user:', user, 'and token:', token ? 'Token exists' : 'No token');

    if (!token) {
      console.error('Cannot perform manual login without a token');
      return;
    }

    // Validate token format
    if (typeof token !== 'string' || token.split('.').length !== 3) {
      console.error('Invalid token format for manual login');
      return;
    }

    // Normalize user data if provided
    let normalizedUser = null;
    if (user) {
      normalizedUser = this.normalizeUserData(user);
      console.log('Normalized user for manual login:', normalizedUser);
    } else {
      console.warn('No user data provided for manual login');
    }

    // Update storage
    this.storage.setItem(STORAGE_KEYS.TOKEN, token);
    if (normalizedUser) {
      this.storage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));
    }

    // Update state
    this.authTokenSubject.next(token);
    this.authStateSubject.next(true);
    if (normalizedUser) {
      this.currentUserSubject.next(normalizedUser);
    }

    console.log('Manual login completed');
    console.log('Auth state after manual login:', this.authStateSubject.value);
    console.log('Auth token after manual login:', this.authTokenSubject.value ? 'Token exists' : 'No token');

    // If we have a token but no user data, try to fetch the user data
    if (!normalizedUser) {
      console.log('Attempting to fetch user data after manual login');
      this.fetchCurrentUser().subscribe({
        next: (user) => {
          if (user) {
            console.log('Successfully fetched user data after manual login');
          } else {
            console.warn('Failed to fetch user data after manual login');
          }
        },
        error: (error) => {
          console.error('Error fetching user data after manual login:', error);
        }
      });
    }
  }

  // Request password reset
  requestPasswordReset(email: string): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/auth/forgot-password`,
      { email },
      {
        headers: new HttpHeaders()
          .set('Accept', 'application/json')
          .set('Content-Type', 'application/json')
      }
    ).pipe(
      catchError(this.handleError)
    );
  }

  // Reset password with token
  resetPassword(data: ResetPasswordData): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/auth/reset-password`,
      data,
      {
        headers: new HttpHeaders()
          .set('Accept', 'application/json')
          .set('Content-Type', 'application/json')
      }
    ).pipe(
      catchError(this.handleError)
    );
  }

  // Google Sign-In
  googleSignIn(): Observable<any> {
    if (typeof window !== 'undefined') {
      this.storage.setItem('auth_redirect', '/profile');

      // Use the API URL directly
      const baseUrl = this.apiUrl;
      const googleAuthUrl = `${baseUrl}/auth/google`;

      console.log('Using Google auth URL:', googleAuthUrl);

      // Return an observable that emits the URL directly
      return of({ url: googleAuthUrl }).pipe(
        tap(response => {
          console.log('Google auth URL ready:', response.url);
        }),
        catchError(error => {
          console.error('Error preparing Google auth URL:', error);
          return throwError(() => new Error('Failed to prepare Google authentication URL'));
        })
      );
    }
    return EMPTY;
  }

  // Handle Google callback
  handleGoogleCallback(code: string): Observable<AuthResponse> {
    // Use the API URL directly
    const baseUrl = this.apiUrl;
    console.log('Handling OAuth callback with URL:', `${baseUrl}/auth/google/callback?code=${code.substring(0, 10)}...`);
    console.log('Full code length:', code.length);

    return this.http.get<AuthResponse>(`${baseUrl}/auth/google/callback?code=${code}`, {
      headers: new HttpHeaders({
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }),
      withCredentials: true
    }).pipe(
      tap(response => {
        console.log('Google callback raw response:', response);
        if (response && response.token) {
          console.log('Valid token received in response');

          // Validate token format before using it
          if (typeof response.token === 'string' && response.token.split('.').length === 3) {
            console.log('Valid JWT token format');

            // Store the token first
            this.storage.setItem(STORAGE_KEYS.TOKEN, response.token);
            this.authTokenSubject.next(response.token);
            this.authStateSubject.next(true);

            // Then handle the user data if available
            if (response.user) {
              console.log('User data received, normalizing and storing');
              const normalizedUser = this.normalizeUserData(response.user);
              this.storage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));
              this.currentUserSubject.next(normalizedUser);
            } else {
              console.warn('No user data in response, will try to fetch from profile endpoint');
              // Try to fetch user data from profile endpoint
              this.fetchCurrentUser().subscribe({
                next: (user) => {
                  if (user) {
                    console.log('Successfully fetched user data from profile endpoint');
                  } else {
                    console.warn('Failed to fetch user data from profile endpoint');
                  }
                },
                error: (error) => {
                  console.error('Error fetching user data from profile endpoint:', error);
                }
              });
            }

            // Start refresh timer with error handling
            try {
              this.startRefreshTokenTimer();
            } catch (e) {
              console.error('Error starting refresh timer:', e);
              // Don't fail the auth process if refresh timer fails
            }
          } else {
            console.warn('Invalid JWT token format received');
          }
        } else {
          console.warn('Response missing token:', response);
        }
      }),
      catchError(error => {
        console.error('Google callback error:', error);
        console.error('Error response:', error.error);
        console.error('Error status:', error.status);
        return this.handleError(error);
      })
    );
  }

  // Facebook Sign-In
  facebookSignIn(): Observable<any> {
    if (typeof window !== 'undefined') {
      this.storage.setItem('auth_redirect', '/profile');

      // Clear any existing auth data to prevent conflicts
      this.storage.removeItem(STORAGE_KEYS.USER);
      this.storage.removeItem(STORAGE_KEYS.TOKEN);
      console.log('Cleared existing auth data from storage');

      // Use the API URL directly
      const baseUrl = this.apiUrl;
      // Add state parameter to identify Facebook callback
      const facebookAuthUrl = `${baseUrl}/auth/facebook?state=facebook`;

      console.log('Using Facebook auth URL with state parameter:', facebookAuthUrl);

      // Return an observable that emits the URL directly
      return of({ url: facebookAuthUrl }).pipe(
        tap(response => {
          console.log('Facebook auth URL ready:', response.url);
        }),
        catchError(error => {
          console.error('Error preparing Facebook auth URL:', error);
          return throwError(() => new Error('Failed to prepare Facebook authentication URL'));
        })
      );
    }
    return EMPTY;
  }

  // Handle Facebook callback
  handleFacebookCallback(code: string): Observable<AuthResponse> {
    // Use the API URL directly
    const baseUrl = this.apiUrl;
    console.log('Handling Facebook callback with URL:', `${baseUrl}/auth/facebook/callback?code=${code.substring(0, 10)}...`);
    console.log('Full code length:', code.length);

    // Include state parameter to identify Facebook callback
    const state = this.storage.getItem('facebook_state') || 'facebook';
    console.log('Using state parameter for Facebook callback:', state);

    return this.http.get<AuthResponse>(`${baseUrl}/auth/facebook/callback?code=${code}&state=${state}`, {
      headers: new HttpHeaders({
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      }),
      withCredentials: true
    }).pipe(
      tap(response => {
        console.log('Facebook callback raw response:', response);

        // Extract token from response
        let token = null;
        if (response && response.token) {
          token = response.token;
        } else if (response && response.data && response.data.token) {
          token = response.data.token;
        }

        if (token) {
          console.log('Token found in response, length:', token.length);

          // Store the token with more lenient validation
          if (typeof token === 'string') {
            console.log('Valid token type, storing in storage and memory');
            this.storage.setItem(STORAGE_KEYS.TOKEN, token);
            this.authTokenSubject.next(token);
            this.authStateSubject.next(true);
          } else {
            console.warn('Invalid token type:', typeof token);
          }

          // Extract and handle user data
          let userData = null;
          if (response.user) {
            userData = response.user;
          } else if (response.data && response.data.user) {
            userData = response.data.user;
          }

          if (userData) {
            console.log('User data found in response');
            const normalizedUser = this.normalizeUserData(userData);
            this.storage.setItem(STORAGE_KEYS.USER, JSON.stringify(normalizedUser));
            this.currentUserSubject.next(normalizedUser);
          } else {
            console.warn('No user data in response, will try to fetch from profile endpoint');
            // Try to fetch user data from profile endpoint
            this.fetchCurrentUser().subscribe({
              next: (user) => {
                if (user) {
                  console.log('Successfully fetched user data from profile endpoint');
                } else {
                  console.warn('Failed to fetch user data from profile endpoint');
                }
              },
              error: (error) => {
                console.error('Error fetching user data from profile endpoint:', error);
              }
            });
          }

          // Start refresh timer with error handling
          try {
            this.startRefreshTokenTimer();
          } catch (e) {
            console.error('Error starting refresh timer:', e);
            // Don't fail the auth process if refresh timer fails
          }
        } else {
          console.warn('No token found in response:', response);
        }
      }),
      catchError(error => {
        console.error('Facebook callback error:', error);
        console.error('Error response:', error.error);
        console.error('Error status:', error.status);
        return this.handleError(error);
      })
    );
  }

  // Token refresh
  refreshToken(): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(`${this.apiUrl}/refresh-token`, {}, {
      headers: this.getAuthHeaders()
    }).pipe(
      tap(response => {
        this.updateStorageAndState(response);
        this.startRefreshTokenTimer();
      }),
      catchError(error => {
        this.logout();
        return throwError(() => error);
      })
    );
  }

  private startRefreshTokenTimer() {
    // Skip if not in browser environment
    if (!isPlatformBrowser(this.platformId)) {
      console.log('Not in browser environment, skipping token refresh timer');
      return;
    }

    // Clear any existing timer
    this.stopRefreshTokenTimer();

    // Get token expiration from JWT
    const token = this.getAuthToken();
    if (token) {
      try {
        // Validate token format before decoding
        const tokenParts = token.split('.');
        if (tokenParts.length !== 3) {
          console.warn('Invalid JWT token format - does not have 3 parts');
          return;
        }

        // Try to decode the token payload
        let tokenPayload;
        try {
          tokenPayload = atob(tokenParts[1]);
        } catch (e) {
          console.warn('Failed to decode JWT token payload:', e);
          return;
        }

        // Try to parse the JSON
        let jwtToken;
        try {
          jwtToken = JSON.parse(tokenPayload);
        } catch (e) {
          console.warn('Failed to parse JWT token JSON:', e);
          return;
        }

        // Check if expiration exists
        if (!jwtToken.exp) {
          console.warn('JWT token does not contain expiration');
          return;
        }

        const expires = new Date(jwtToken.exp * 1000);
        const now = Date.now();

        // Check if token is already expired
        if (expires.getTime() <= now) {
          console.warn('JWT token is already expired');
          return;
        }

        const timeout = expires.getTime() - now - (60 * 1000); // Refresh 1 minute before expiry
        console.log(`Token will expire at ${expires.toISOString()}, setting refresh timeout for ${timeout}ms`);
        this.refreshTokenTimeout = setTimeout(() => this.refreshToken().subscribe(), timeout);
      } catch (error) {
        console.error('Error setting up token refresh timer:', error);
        // Don't log out the user if token refresh timer setup fails
      }
    }
  }

  private stopRefreshTokenTimer() {
    if (this.refreshTokenTimeout) {
      clearTimeout(this.refreshTokenTimeout);
    }
  }



  forgotPassword(email: string): Observable<any> {
    return this.http.post<any>(
      `${this.apiUrl}/auth/forgot-password`,
      { email },
      {
        headers: new HttpHeaders()
          .set('Accept', 'application/json')
          .set('Content-Type', 'application/json')
      }
    ).pipe(
      catchError(this.handleError)
    );
  }

  updatePassword(currentPassword: string, newPassword: string): Observable<any> {
    return this.http.put<any>(
      `${this.apiUrl}/user/password`,
      { current_password: currentPassword, password: newPassword, password_confirmation: newPassword },
      {
        headers: this.getAuthHeaders(),
        withCredentials: true
      }
    ).pipe(
      catchError((error: HttpErrorResponse) => {
        // Special handling for password update errors
        let errorMessage = 'An unknown error occurred';

        if (error.error instanceof ErrorEvent) {
          errorMessage = `Client error: ${error.error.message}`;
        } else {
          console.log('Password update error:', error);

          if (error.status === 401) {
            // Don't log out for incorrect current password
            if (error.error?.message?.includes('Current password is incorrect')) {
              errorMessage = 'Current password is incorrect';
            } else {
              // For other 401 errors, use the standard handler
              return this.handleError(error);
            }
          } else if (error.status === 422) {
            errorMessage = 'Validation error: Please check your password requirements';
          } else if (error.status === 404) {
            errorMessage = 'Resource not found';
          } else {
            // For other errors, use the standard handler
            return this.handleError(error);
          }
        }

        console.error('Password update error:', errorMessage);
        return throwError(() => new Error(errorMessage));
      })
    );
  }

  /**
   * Accept terms and conditions
   * Updates the user's terms_accepted and terms_accepted_at fields
   */
  acceptTerms(): Observable<AuthResponse> {
    return this.http.post<AuthResponse>(
      `${this.apiUrl}/accept-terms`,
      {},
      {
        headers: this.getAuthHeaders(),
        withCredentials: true
      }
    ).pipe(
      tap(response => {
        if (response.user) {
          // Update local user data
          const user = this.normalizeUserData(response.user);
          this.currentUserSubject.next(user);

          // Update storage
          if (isPlatformBrowser(this.platformId)) {
            this.storage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
          }
        }
      }),
      catchError(this.handleError)
    );
  }
}
